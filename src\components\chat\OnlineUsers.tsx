import React from 'react';
import { useChat } from '../../contexts/ChatContext';

export default function OnlineUsers() {
  const { currentRoom, onlineUsers, isConnected } = useChat();

  if (!currentRoom) {
    return null;
  }

  const roomOnlineUsers = onlineUsers[currentRoom.id] || [];

  return (
    <div className="w-64 bg-gray-50 border-l border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Online Users</h3>
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} 
               title={isConnected ? 'Connected' : 'Disconnected'} />
        </div>
        <p className="text-sm text-gray-600 mt-1">
          {roomOnlineUsers.length} {roomOnlineUsers.length === 1 ? 'user' : 'users'} online
        </p>
      </div>

      {/* Online Users List */}
      <div className="flex-1 overflow-y-auto p-4">
        {roomOnlineUsers.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <svg className="w-12 h-12 mx-auto mb-3 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <p className="text-sm">No users online</p>
          </div>
        ) : (
          <div className="space-y-2">
            {roomOnlineUsers.map((user) => (
              <div
                key={user.id}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {/* Avatar */}
                <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
                  {user.username.charAt(0).toUpperCase()}
                </div>
                
                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.username}
                  </p>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                    <p className="text-xs text-gray-500">Online</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Room Info */}
      <div className="p-4 border-t border-gray-200 bg-gray-100">
        <div className="text-sm text-gray-600">
          <div className="flex items-center justify-between mb-1">
            <span>Room:</span>
            <span className="font-medium">#{currentRoom.name}</span>
          </div>
          {currentRoom.description && (
            <div className="text-xs text-gray-500 mt-1">
              {currentRoom.description}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
