import express from 'express';
import { createServer } from 'http';
import { Server } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';

// Import routes
import authRoutes from './routes/auth.js';
import roomRoutes from './routes/rooms.js';
import messageRoutes from './routes/messages.js';

// Import middleware
import { authenticateToken } from './middleware/auth.js';

// Import socket handlers
import {
  handleAuthentication,
  handleJoinRoom,
  handleLeaveRoom,
  handleSendMessage,
  handleDisconnect,
  AuthenticatedSocket,
} from './socket/socketHandlers.js';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);

// Configure CORS
const corsOrigins = process.env.CORS_ORIGINS?.split(',') || ['http://localhost:5173'];
const corsOptions = {
  origin: corsOrigins,
  credentials: true,
};

app.use(cors(corsOptions));

// Configure Socket.IO with CORS
const io = new Server(server, {
  cors: corsOptions,
});

// Middleware
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/rooms', roomRoutes);
app.use('/api/messages', messageRoutes);

// Protected route example
app.get('/api/protected', authenticateToken, (req, res) => {
  res.json({ message: 'This is a protected route', user: req.user });
});

// Socket.IO connection handling
io.on('connection', async (socket: AuthenticatedSocket) => {
  console.log('New socket connection:', socket.id);

  // Handle authentication
  socket.on('authenticate', async (token: string) => {
    const isAuthenticated = await handleAuthentication(socket, token);
    if (isAuthenticated) {
      socket.emit('authenticated', { user: socket.user });
    } else {
      socket.emit('authenticationFailed', { message: 'Invalid token' });
      socket.disconnect();
    }
  });

  // Handle joining a room
  socket.on('joinRoom', async (roomId: string) => {
    await handleJoinRoom(io, socket, roomId);
  });

  // Handle leaving a room
  socket.on('leaveRoom', async (roomId: string) => {
    await handleLeaveRoom(io, socket, roomId);
  });

  // Handle sending messages
  socket.on('sendMessage', async (data) => {
    await handleSendMessage(io, socket, data);
  });

  // Handle disconnect
  socket.on('disconnect', async () => {
    await handleDisconnect(io, socket);
  });

  // Handle errors
  socket.on('error', (error) => {
    console.error('Socket error:', error);
  });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📡 Socket.IO server ready`);
  console.log(`🌍 CORS enabled for: ${corsOrigins.join(', ')}`);
});
