import { Server, Socket } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import { verifyToken, JwtPayload } from '../utils/auth.js';
import { validateRequest, sendMessageSchema } from '../utils/validation.js';

const prisma = new PrismaClient();

// Store online users and their socket connections
const onlineUsers = new Map<string, { socketId: string; user: JwtPayload }>();
const userRooms = new Map<string, Set<string>>(); // userId -> Set of roomIds

export interface AuthenticatedSocket extends Socket {
  user?: JwtPayload;
}

/**
 * Handle socket authentication
 */
export async function handleAuthentication(socket: AuthenticatedSocket, token: string): Promise<boolean> {
  try {
    const user = verifyToken(token);
    socket.user = user;
    
    // Store user as online
    onlineUsers.set(user.userId, { socketId: socket.id, user });
    
    console.log(`User ${user.username} connected with socket ${socket.id}`);
    return true;
  } catch (error) {
    console.error('Socket authentication failed:', error);
    return false;
  }
}

/**
 * Handle user joining a room
 */
export async function handleJoinRoom(io: Server, socket: AuthenticatedSocket, roomId: string): Promise<void> {
  try {
    if (!socket.user) {
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    // Verify user is a member of the room
    const membership = await prisma.roomMember.findUnique({
      where: {
        userId_roomId: {
          userId: socket.user.userId,
          roomId,
        },
      },
    });

    if (!membership) {
      socket.emit('error', { message: 'Access denied. You must be a member of this room.' });
      return;
    }

    // Join the socket room
    await socket.join(roomId);
    
    // Track user's rooms
    if (!userRooms.has(socket.user.userId)) {
      userRooms.set(socket.user.userId, new Set());
    }
    userRooms.get(socket.user.userId)!.add(roomId);

    // Get room info
    const room = await prisma.room.findUnique({
      where: { id: roomId },
      select: { name: true },
    });

    // Notify other users in the room
    socket.to(roomId).emit('userJoined', {
      user: {
        id: socket.user.userId,
        username: socket.user.username,
      },
      room: {
        id: roomId,
        name: room?.name,
      },
      timestamp: new Date().toISOString(),
    });

    // Send confirmation to the user
    socket.emit('joinedRoom', {
      roomId,
      roomName: room?.name,
    });

    // Send updated online users list for this room
    await sendOnlineUsersUpdate(io, roomId);

    console.log(`User ${socket.user.username} joined room ${roomId}`);
  } catch (error) {
    console.error('Join room error:', error);
    socket.emit('error', { message: 'Failed to join room' });
  }
}

/**
 * Handle user leaving a room
 */
export async function handleLeaveRoom(io: Server, socket: AuthenticatedSocket, roomId: string): Promise<void> {
  try {
    if (!socket.user) return;

    // Leave the socket room
    await socket.leave(roomId);
    
    // Remove from user's rooms tracking
    if (userRooms.has(socket.user.userId)) {
      userRooms.get(socket.user.userId)!.delete(roomId);
    }

    // Get room info
    const room = await prisma.room.findUnique({
      where: { id: roomId },
      select: { name: true },
    });

    // Notify other users in the room
    socket.to(roomId).emit('userLeft', {
      user: {
        id: socket.user.userId,
        username: socket.user.username,
      },
      room: {
        id: roomId,
        name: room?.name,
      },
      timestamp: new Date().toISOString(),
    });

    // Send updated online users list for this room
    await sendOnlineUsersUpdate(io, roomId);

    console.log(`User ${socket.user.username} left room ${roomId}`);
  } catch (error) {
    console.error('Leave room error:', error);
  }
}

/**
 * Handle sending a message
 */
export async function handleSendMessage(io: Server, socket: AuthenticatedSocket, data: any): Promise<void> {
  try {
    if (!socket.user) {
      socket.emit('error', { message: 'Authentication required' });
      return;
    }

    const { content, roomId } = validateRequest(sendMessageSchema, data);

    // Verify user is a member of the room
    const membership = await prisma.roomMember.findUnique({
      where: {
        userId_roomId: {
          userId: socket.user.userId,
          roomId,
        },
      },
    });

    if (!membership) {
      socket.emit('error', { message: 'Access denied. You must be a member of this room.' });
      return;
    }

    // Save message to database
    const message = await prisma.message.create({
      data: {
        content,
        userId: socket.user.userId,
        roomId,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });

    // Broadcast message to all users in the room
    io.to(roomId).emit('newMessage', {
      id: message.id,
      content: message.content,
      createdAt: message.createdAt,
      user: message.user,
      roomId: message.roomId,
    });

    console.log(`Message sent by ${socket.user.username} in room ${roomId}`);
  } catch (error) {
    console.error('Send message error:', error);
    if (error instanceof Error && error.message.startsWith('Validation failed')) {
      socket.emit('error', { message: error.message });
      return;
    }
    socket.emit('error', { message: 'Failed to send message' });
  }
}

/**
 * Handle user disconnect
 */
export async function handleDisconnect(io: Server, socket: AuthenticatedSocket): Promise<void> {
  try {
    if (!socket.user) return;

    // Remove from online users
    onlineUsers.delete(socket.user.userId);

    // Get all rooms the user was in
    const userRoomSet = userRooms.get(socket.user.userId);
    if (userRoomSet) {
      // Notify all rooms about user leaving
      for (const roomId of userRoomSet) {
        socket.to(roomId).emit('userLeft', {
          user: {
            id: socket.user.userId,
            username: socket.user.username,
          },
          room: { id: roomId },
          timestamp: new Date().toISOString(),
        });

        // Send updated online users list for each room
        await sendOnlineUsersUpdate(io, roomId);
      }
      
      // Clear user's rooms
      userRooms.delete(socket.user.userId);
    }

    console.log(`User ${socket.user.username} disconnected`);
  } catch (error) {
    console.error('Disconnect error:', error);
  }
}

/**
 * Send updated online users list for a room
 */
async function sendOnlineUsersUpdate(io: Server, roomId: string): Promise<void> {
  try {
    // Get all members of the room
    const roomMembers = await prisma.roomMember.findMany({
      where: { roomId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });

    // Filter for online members
    const onlineMembers = roomMembers
      .filter(member => onlineUsers.has(member.userId))
      .map(member => member.user);

    // Send to all users in the room
    io.to(roomId).emit('onlineUsersUpdate', {
      roomId,
      onlineUsers: onlineMembers,
    });
  } catch (error) {
    console.error('Online users update error:', error);
  }
}
