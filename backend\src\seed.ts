import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create test users
  const hashedPassword = await bcrypt.hash('password123', 10);
  
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'alice',
      password: hashedPassword,
    },
  });

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'bob',
      password: hashedPassword,
    },
  });

  const user3 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'charlie',
      password: hashedPassword,
    },
  });

  // Create test rooms
  const generalRoom = await prisma.room.upsert({
    where: { name: 'general' },
    update: {},
    create: {
      name: 'general',
      description: 'General discussion room',
      isPrivate: false,
    },
  });

  const randomRoom = await prisma.room.upsert({
    where: { name: 'random' },
    update: {},
    create: {
      name: 'random',
      description: 'Random conversations',
      isPrivate: false,
    },
  });

  const techRoom = await prisma.room.upsert({
    where: { name: 'tech-talk' },
    update: {},
    create: {
      name: 'tech-talk',
      description: 'Technology discussions',
      isPrivate: false,
    },
  });

  // Add users to rooms
  await prisma.roomMember.upsert({
    where: {
      userId_roomId: {
        userId: user1.id,
        roomId: generalRoom.id,
      },
    },
    update: {},
    create: {
      userId: user1.id,
      roomId: generalRoom.id,
    },
  });

  await prisma.roomMember.upsert({
    where: {
      userId_roomId: {
        userId: user2.id,
        roomId: generalRoom.id,
      },
    },
    update: {},
    create: {
      userId: user2.id,
      roomId: generalRoom.id,
    },
  });

  await prisma.roomMember.upsert({
    where: {
      userId_roomId: {
        userId: user1.id,
        roomId: techRoom.id,
      },
    },
    update: {},
    create: {
      userId: user1.id,
      roomId: techRoom.id,
    },
  });

  // Create some sample messages
  await prisma.message.create({
    data: {
      content: 'Welcome to the general room! 👋',
      userId: user1.id,
      roomId: generalRoom.id,
    },
  });

  await prisma.message.create({
    data: {
      content: 'Hey everyone! Great to be here.',
      userId: user2.id,
      roomId: generalRoom.id,
    },
  });

  await prisma.message.create({
    data: {
      content: 'Anyone working on interesting projects lately?',
      userId: user1.id,
      roomId: techRoom.id,
    },
  });

  console.log('✅ Database seeded successfully!');
  console.log('Test users created:');
  console.log('- <EMAIL> (password: password123)');
  console.log('- <EMAIL> (password: password123)');
  console.log('- <EMAIL> (password: password123)');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
