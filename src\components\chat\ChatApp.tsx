import React, { useEffect, useState } from 'react';
import { useChat } from '../../contexts/ChatContext';
import { Room } from '../../types';
import RoomList from './RoomList';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import OnlineUsers from './OnlineUsers';

export default function ChatApp() {
  const { loadRooms, setCurrentRoom, currentRoom } = useChat();
  const [showOnlineUsers, setShowOnlineUsers] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile view
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 1024) {
        setShowOnlineUsers(false);
      } else {
        setShowOnlineUsers(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load rooms on mount
  useEffect(() => {
    loadRooms();
  }, [loadRooms]);

  const handleRoomSelect = (room: Room) => {
    setCurrentRoom(room);
  };

  const toggleOnlineUsers = () => {
    setShowOnlineUsers(!showOnlineUsers);
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Room List - Hidden on mobile when room is selected */}
      <div className={`${isMobile && currentRoom ? 'hidden' : 'block'}`}>
        <RoomList onRoomSelect={handleRoomSelect} />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        {isMobile && currentRoom && (
          <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
            <button
              onClick={() => setCurrentRoom(null)}
              className="text-gray-600 hover:text-gray-900"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 className="text-lg font-semibold">#{currentRoom.name}</h1>
            <button
              onClick={toggleOnlineUsers}
              className="text-gray-600 hover:text-gray-900"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </button>
          </div>
        )}

        {/* Desktop Header */}
        {!isMobile && currentRoom && (
          <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">#{currentRoom.name}</h1>
              {currentRoom.description && (
                <span className="ml-3 text-gray-500">— {currentRoom.description}</span>
              )}
            </div>
            <button
              onClick={toggleOnlineUsers}
              className={`p-2 rounded-lg transition-colors ${
                showOnlineUsers 
                  ? 'bg-indigo-100 text-indigo-600' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="Toggle online users"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </button>
          </div>
        )}

        {/* Chat Content */}
        <div className="flex-1 flex min-h-0">
          {/* Messages Area */}
          <div className="flex-1 flex flex-col min-w-0">
            <MessageList />
            <MessageInput />
          </div>

          {/* Online Users - Show/hide based on state */}
          {showOnlineUsers && currentRoom && (
            <div className={`${isMobile ? 'absolute inset-0 z-10 bg-white' : 'block'}`}>
              {isMobile && (
                <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                  <h2 className="text-lg font-semibold">Online Users</h2>
                  <button
                    onClick={() => setShowOnlineUsers(false)}
                    className="text-gray-600 hover:text-gray-900"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              )}
              <OnlineUsers />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
