import { Router, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken } from '../middleware/auth.js';
import { validateRequest, createRoomSchema, joinRoomSchema } from '../utils/validation.js';

const router = Router();
const prisma = new PrismaClient();

// Apply authentication middleware to all room routes
router.use(authenticateToken);

/**
 * GET /api/rooms
 * Get all public rooms and rooms the user is a member of
 */
router.get('/', async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const rooms = await prisma.room.findMany({
      where: {
        OR: [
          { isPrivate: false },
          {
            members: {
              some: {
                userId: req.user.userId,
              },
            },
          },
        ],
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
              },
            },
          },
        },
        _count: {
          select: {
            messages: true,
            members: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    res.json({ rooms });
  } catch (error) {
    console.error('Get rooms error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * POST /api/rooms
 * Create a new room
 */
router.post('/', async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const { name, description, isPrivate } = validateRequest(createRoomSchema, req.body);

    // Check if room name already exists
    const existingRoom = await prisma.room.findUnique({
      where: { name },
    });

    if (existingRoom) {
      res.status(400).json({ error: 'Room name already exists' });
      return;
    }

    // Create room and add creator as member
    const room = await prisma.room.create({
      data: {
        name,
        description,
        isPrivate,
        members: {
          create: {
            userId: req.user.userId,
          },
        },
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
              },
            },
          },
        },
        _count: {
          select: {
            messages: true,
            members: true,
          },
        },
      },
    });

    res.status(201).json({
      message: 'Room created successfully',
      room,
    });
  } catch (error) {
    console.error('Create room error:', error);
    if (error instanceof Error && error.message.startsWith('Validation failed')) {
      res.status(400).json({ error: error.message });
      return;
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * POST /api/rooms/:roomId/join
 * Join a room
 */
router.post('/:roomId/join', async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const { roomId } = req.params;

    // Check if room exists
    const room = await prisma.room.findUnique({
      where: { id: roomId },
    });

    if (!room) {
      res.status(404).json({ error: 'Room not found' });
      return;
    }

    // Check if user is already a member
    const existingMember = await prisma.roomMember.findUnique({
      where: {
        userId_roomId: {
          userId: req.user.userId,
          roomId,
        },
      },
    });

    if (existingMember) {
      res.status(400).json({ error: 'Already a member of this room' });
      return;
    }

    // Add user to room
    await prisma.roomMember.create({
      data: {
        userId: req.user.userId,
        roomId,
      },
    });

    res.json({ message: 'Successfully joined room' });
  } catch (error) {
    console.error('Join room error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * DELETE /api/rooms/:roomId/leave
 * Leave a room
 */
router.delete('/:roomId/leave', async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    const { roomId } = req.params;

    // Check if user is a member
    const membership = await prisma.roomMember.findUnique({
      where: {
        userId_roomId: {
          userId: req.user.userId,
          roomId,
        },
      },
    });

    if (!membership) {
      res.status(400).json({ error: 'Not a member of this room' });
      return;
    }

    // Remove user from room
    await prisma.roomMember.delete({
      where: {
        userId_roomId: {
          userId: req.user.userId,
          roomId,
        },
      },
    });

    res.json({ message: 'Successfully left room' });
  } catch (error) {
    console.error('Leave room error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
