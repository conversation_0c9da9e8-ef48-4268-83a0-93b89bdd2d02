{"name": "chat-app-backend", "version": "1.0.0", "description": "Backend server for real-time chat application", "type": "module", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/seed.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "socket.io": "^4.8.1", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.10.5", "prisma": "^6.13.0", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "keywords": ["chat", "websocket", "real-time", "express", "socket.io"], "author": "", "license": "MIT"}