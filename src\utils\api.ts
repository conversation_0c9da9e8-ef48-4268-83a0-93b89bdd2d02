import { 
  AuthResponse, 
  LoginRequest, 
  RegisterRequest, 
  Room, 
  Message, 
  CreateRoomRequest 
} from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

class ApiError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new ApiError(response.status, errorData.error || 'Request failed');
  }

  return response.json();
}

// Auth API
export const authApi = {
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    return apiRequest<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    return apiRequest<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  getMe: async () => {
    return apiRequest<{ user: any }>('/auth/me');
  },
};

// Rooms API
export const roomsApi = {
  getRooms: async (): Promise<{ rooms: Room[] }> => {
    return apiRequest<{ rooms: Room[] }>('/rooms');
  },

  createRoom: async (data: CreateRoomRequest): Promise<{ message: string; room: Room }> => {
    return apiRequest<{ message: string; room: Room }>('/rooms', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  joinRoom: async (roomId: string): Promise<{ message: string }> => {
    return apiRequest<{ message: string }>(`/rooms/${roomId}/join`, {
      method: 'POST',
    });
  },

  leaveRoom: async (roomId: string): Promise<{ message: string }> => {
    return apiRequest<{ message: string }>(`/rooms/${roomId}/leave`, {
      method: 'DELETE',
    });
  },
};

// Messages API
export const messagesApi = {
  getMessages: async (
    roomId: string, 
    page = 1, 
    limit = 50
  ): Promise<{
    messages: Message[];
    pagination: {
      page: number;
      limit: number;
      totalMessages: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
    };
  }> => {
    return apiRequest<any>(`/messages/${roomId}?page=${page}&limit=${limit}`);
  },

  getRecentMessages: async (roomId: string): Promise<{ messages: Message[] }> => {
    return apiRequest<{ messages: Message[] }>(`/messages/${roomId}/recent`);
  },
};

export { ApiError };
