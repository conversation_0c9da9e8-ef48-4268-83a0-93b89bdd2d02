// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  messages    Message[]
  roomMembers RoomMember[]

  @@map("users")
}

model Room {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isPrivate   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  messages Message[]
  members  RoomMember[]

  @@map("rooms")
}

model Message {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Foreign keys
  userId String
  roomId String

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model RoomMember {
  id       String   @id @default(cuid())
  joinedAt DateTime @default(now())

  // Foreign keys
  userId String
  roomId String

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  room Room @relation(fields: [roomId], references: [id], onDelete: Cascade)

  // Ensure a user can only be a member of a room once
  @@unique([userId, roomId])
  @@map("room_members")
}
