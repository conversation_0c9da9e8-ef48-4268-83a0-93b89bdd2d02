import React from 'react'
import { AuthProvider } from './contexts/AuthContext'
import { ChatProvider } from './contexts/ChatContext'
import ProtectedRoute from './components/auth/ProtectedRoute'
import ChatApp from './components/chat/ChatApp'

function App() {
  return (
    <AuthProvider>
      <ChatProvider>
        <ProtectedRoute>
          <ChatApp />
        </ProtectedRoute>
      </ChatProvider>
    </AuthProvider>
  )
}

export default App
